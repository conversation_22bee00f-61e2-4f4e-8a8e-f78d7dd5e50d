/* 联系我们 */
.contact {
  padding: 100px 0;
  text-align: center;
}

.contact h2 {
  font-size: 40px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 60px;
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
}

.contact-card {
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(4px);
  border-radius: 20px;
  box-shadow: 0px 12px 32px rgba(153, 172, 255, 0.14);
  padding: 60px 40px;
  text-align: center;
}

.contact-icon {
  width: 120px;
  height: 120px;
  margin: 0 auto 10px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contact-icon img {
  width: 100%;
  height: 100%;
}

.contact-card h3 {
  font-size: 26px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 15px;
}

.contact-card p {
  font-size: 16px;
  color: #8d9094;
  line-height: 1.5;
}
/* 页脚 */
.footer {
  background: #0d185c;
  color: #ffffff;
  padding: 60px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-section h4 {
  font-size: 14px;
  font-weight: 600;
  background: linear-gradient(to right, #18a2f2, #1755b5);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 20px;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 10px;
}

.footer-section ul li a {
  color: #2f3133;
  text-decoration: none;
  font-size: 12px;
}

.footer-section ul li a:hover {
  color: #18a2f2;
}

.contact-info p {
  margin-bottom: 10px;
  font-size: 12px;
  color: #2f3133;
}

.social-media {
  display: flex;
  gap: 20px;
}

.qr-code {
  text-align: center;
}

.qr-code img {
  width: 74px;
  height: 74px;
  margin-bottom: 10px;
}

.qr-code p {
  font-size: 12px;
  color: #2f3133;
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
  font-size: 14px;
  color: #2f3133;
}
