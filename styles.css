* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #2F3133;
    background: #F8FBFF;
}

.container {
    max-width: 1380px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 导航栏 */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(4px);
    z-index: 1000;
    padding: 10px 0;
}

.nav-container {
    max-width: 1280px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.logo img {
    height: 40px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 40px;
}

.nav-menu a {
    text-decoration: none;
    color: #2F3133;
    font-size: 12px;
    font-weight: 400;
    transition: color 0.3s;
}

.nav-menu a:hover {
    color: #18A2F2;
}

/* 主横幅 */
.hero {
    height: 660px;
    background: url('./images/banner.jpg') center/cover;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    margin-top: 60px;
}

.hero-content h1 {
    font-size: 68px;
    font-weight: 600;
    margin-bottom: 20px;
}

.hero-content h2 {
    font-size: 68px;
    font-weight: 600;
    margin-bottom: 20px;
}

.hero-content h3 {
    font-size: 68px;
    font-weight: 600;
    background: linear-gradient(45deg, #189FF0, #134499);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* 统计数据 */
.stats {
    padding: 60px 0;
}

.stats-container {
    background: rgba(255, 255, 255, 0.61);
    backdrop-filter: blur(4px);
    border-radius: 20px;
    box-shadow: 0px 12px 32px rgba(153, 172, 255, 0.14);
    padding: 40px;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 40px;
    text-align: center;
}

.stat-item h3 {
    font-size: 36px;
    font-weight: 600;
    background: linear-gradient(to right, #18A2F2, #1755B5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 10px;
}

.stat-item p {
    font-size: 18px;
    color: #2F3133;
}

/* 关于我们 */
.about {
    padding: 100px 0;
    text-align: center;
}

.about h2 {
    font-size: 40px;
    font-weight: 600;
    background: linear-gradient(to right, #18A2F2, #1755B5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 30px;
}

.about-description {
    font-size: 16px;
    color: #8D9094;
    max-width: 715px;
    margin: 0 auto 60px;
    line-height: 1.5;
}

.about-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(305px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.feature-card {
    background: #FFFFFF;
    border-radius: 15px;
    padding: 40px 20px;
    text-align: center;
    box-shadow: 0px 12px 32px rgba(153, 172, 255, 0.14);
}

.feature-icon {
    width: 150px;
    height: 150px;
    margin: 0 auto 20px;
    /*background: #f0f0f0;*/
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.feature-icon img {
    width: 100%;
    height: 100%;
}

.feature-card h3 {
    font-size: 32px;
    font-weight: 600;
    background: linear-gradient(to right, #18A2F2, #1755B5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 20px;
}

.feature-card p {
    font-size: 16px;
    color: #8D9094;
    line-height: 1.5;
}

/* 资质认证 */
.aptitude {
    padding: 100px 0;
    background: #f8fbff;
}

.aptitude h2 {
    font-size: 40px;
    font-weight: 600;
    background: linear-gradient(to right, #18A2F2, #1755B5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-align: center;
    margin-bottom: 60px;
}

.certificates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(190px, 1fr));
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.certificate-card {
    /*background: #ffffff;*/
    border-radius: 15px;
    padding: 20px 20px 15px 20px;
    box-shadow: 0px 8px 24px rgba(24, 162, 242, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
    cursor: pointer;
    display: flex;
    flex-direction: column;
}

.certificate-card:hover {
    transform: translateY(-5px);
    box-shadow: 0px 12px 32px rgba(24, 162, 242, 0.2);
}

.certificate-card img {
    width: 100%;
    height: auto;
    border-radius: 10px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.certificate-card:hover img {
    transform: scale(1.05);
}

.certificate-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(24, 162, 242, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 15px;
}

.certificate-card:hover .certificate-overlay {
    opacity: 1;
}

.view-text {
    color: white;
    font-size: 16px;
    font-weight: 600;
    text-align: center;
}

.certificate-title {
    margin-top: 15px;
    font-size: 16px;
    font-weight: 600;
    color: #1755B5;
    text-align: center;
    line-height: 1.4;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    animation: fadeIn 0.3s ease;
}

.modal-content {
    position: relative;
    margin: 5% auto;
    padding: 0;
    width: 90%;
    max-width: 800px;
    background: white;
    border-radius: 15px;
    overflow: hidden;
    animation: slideIn 0.3s ease;
}

.modal-content img {
    width: 100%;
    height: auto;
    display: block;
}

.close {
    position: absolute;
    top: 15px;
    right: 25px;
    color: white;
    font-size: 35px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.close:hover {
    background: rgba(0, 0, 0, 0.8);
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* 解决方案轮播 */
.solutions {
    padding: 100px 0;
    text-align: center;
    background: linear-gradient(180deg, rgba(235, 245, 255, 0) 0%, rgba(225, 240, 255, 1) 50%, rgba(255, 255, 255, 0) 100%);
}

.solutions h2 {
    font-size: 40px;
    font-weight: 600;
    background: linear-gradient(to right, #18A2F2, #1755B5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 60px;
}

.carousel-container {
    position: relative;
    max-width: 1280px;
    margin: 0 auto;
}

.carousel {
    position: relative;
    overflow: hidden;
}

.carousel-inner {
    display: flex;
    transition: transform 0.5s ease;
}

.carousel-item {
    min-width: 100%;
    display: flex;
    justify-content: center;
    gap: 30px;
    padding: 0 20px;
}

/* 解决方案卡片样式 */
.solution-card {
    background: linear-gradient(135deg, rgba(24, 162, 242, 0.1) 0%, rgba(23, 85, 181, 0.15) 100%);
    border-radius: 20px;
    padding: 40px 30px;
    text-align: center;
    box-shadow: 0px 8px 24px rgba(24, 162, 242, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    flex: 1;
    max-width: 380px;
    min-height: 420px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.solution-card:hover {
    transform: translateY(-5px);
    box-shadow: 0px 12px 32px rgba(24, 162, 242, 0.25);
}

.solution-card-image {
    width: 100%;
    height: 200px;
    border-radius: 15px;
    overflow: hidden;
    margin-bottom: 25px;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.solution-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 15px;
}

.solution-card-content h3 {
    font-size: 20px;
    font-weight: 600;
    color: #1755B5;
    margin-bottom: 15px;
    line-height: 1.3;
}

.solution-card-content p {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    text-align: left;
}

.carousel-indicators {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 40px;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(24, 162, 242, 0.3);
    cursor: pointer;
    transition: all 0.3s;
}

.indicator.active {
    background: linear-gradient(to right, #18A2F2, #1755B5);
}

.carousel-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: #FFFFFF;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 24px;
    color: #18A2F2;
    cursor: pointer;
    box-shadow: 0px 4px 12px rgba(24, 162, 242, 0.2);
    transition: all 0.3s;
    z-index: 10;
}

.carousel-btn:hover {
    background: #18A2F2;
    color: white;
    box-shadow: 0px 6px 16px rgba(24, 162, 242, 0.3);
}

.carousel-btn.prev {
    left: 10px;
}

.carousel-btn.next {
    right: 10px;
}

/* OTC轮播保持原有样式 */
#otcCarousel .carousel-item {
    display: flex;
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(4px);
    border-radius: 20px;
    box-shadow: 0px 12px 32px rgba(153, 172, 255, 0.14);
    overflow: hidden;
}

#otcCarousel .carousel-content {
    flex: 1;
    padding: 60px 40px;
    text-align: left;
}

#otcCarousel .carousel-content h3 {
    font-size: 24px;
    font-weight: 600;
    background: linear-gradient(to right, #18A2F2, #1755B5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 30px;
}

#otcCarousel .carousel-content p {
    font-size: 16px;
    color: #8D9094;
    line-height: 1.5;
}

#otcCarousel .carousel-image {
    flex: 2;
    background-size: cover;
    background-position: center;
    min-height: 400px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#otcCarousel .carousel-image img {
    width: 90%;
    height: 90%;
    border-radius: 20px;
}

/* OTC轮播区域 */
.otc-section {
    padding: 50px 0;
}

.otc-carousel .carousel-item {
    background: rgba(255, 255, 255, 0.3);
}

/* 行业资讯 */
.news {
    padding: 100px 0;
    text-align: center;
}

.news h2 {
    font-size: 40px;
    font-weight: 600;
    background: linear-gradient(to right, #18A2F2, #1755B5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 60px;
}

.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
    gap: 40px;
}

.news-card {
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(4px);
    border-radius: 20px;
    box-shadow: 0px 12px 32px rgba(153, 172, 255, 0.14);
    overflow: hidden;
    display: flex;
    text-align: left;
}

.news-card img {
    width: 300px;
    height: 200px;
    object-fit: cover;
}

.news-content {
    padding: 30px;
    flex: 1;
}

.news-content h3 {
    font-size: 26px;
    font-weight: 600;
    color: #000000;
    margin-bottom: 15px;
}

.news-content p {
    font-size: 16px;
    color: #8D9094;
    line-height: 1.5;
    margin-bottom: 20px;
}

.news-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.date {
    font-size: 16px;
    color: #000000;
}

.read-more {
    color: #18A2F2;
    text-decoration: none;
    font-size: 16px;
}

.read-more:hover {
    text-decoration: underline;
}


/* 响应式设计 */
@media (max-width: 768px) {
    .hero-content h1,
    .hero-content h2,
    .hero-content h3 {
        font-size: 36px;
    }
    
    .stats-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
    
    .about-features {
        grid-template-columns: 1fr;
    }
    
    .news-grid {
        grid-template-columns: 1fr;
    }
    
    .news-card {
        flex-direction: column;
    }
    
    .news-card img {
        width: 100%;
        height: 200px;
    }
    
    .contact-grid {
        grid-template-columns: 1fr;
    }
    
    .carousel-btn {
        display: none;
    }

    .solution-card {
        max-width: 100%;
        min-height: 350px;
        padding: 30px 20px;
    }

    .solution-card-image {
        height: 150px;
        margin-bottom: 20px;
    }

    .solution-card-content h3 {
        font-size: 18px;
        margin-bottom: 12px;
    }

    .solution-card-content p {
        font-size: 13px;
    }

    .certificates-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        padding: 0 20px;
    }

    .certificate-card {
        padding: 15px 15px 12px 15px;
    }

    .certificate-title {
        font-size: 14px;
        margin-top: 12px;
        min-height: 40px;
    }

    .aptitude h2 {
        font-size: 32px;
        margin-bottom: 40px;
    }
}

/* 轮播特定样式 */
.solutions-carousel .carousel-item {
    min-height: auto;
}

.otc-carousel .carousel-item {
    min-height: 500px;
}

.contact {
    background: #0D185C;
}