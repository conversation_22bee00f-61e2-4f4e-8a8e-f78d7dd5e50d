// 轮播数据
const solutionsData = [
    {
        title: "工业互联网标识解析平台",
        description: "中药行业工业互联网标识应用平台，是基于工业互联网标识解析中药行业二级节点建设的工业互联网平台，旨在以标识为抓手，为中药全产业链提供唯一的数字身份识别、全流程数据贯通与协同化智慧服务。",
        gradient: "linear-gradient(45deg, #A1C7FF, #D2F8FF)",
        mockupImage: "./images/solution1.png" // 显示登录界面的mockup
    },
    {
        title: "一体化数智中台",
        description: "形成业务→数据→知识→应用闭环，帮助企业发动数据引擎，兑现数据价值基于云边一体协同架构，通过内置的丰富开发套件，助力企业沉淀自己的专业算法与模型支持实时设备数据传输和全域业务系统库表级同步，统一数据口径打破数字孤岛",
        gradient: "linear-gradient(45deg, #E2EDFF, #E2EDFF)",
        mockupImage: "./images/solution2.jpg" // 平台界面mockup
    },
    {
        title: "中药材GAP云平台",
        description: "中药材GAP云平台依新版规范，覆盖种源、种植、采收、加工、检测、流通等环节，实现种植全生命周期数字化管控。融合物联网、IOT、GIS等技术，全程监控农事行为，保障数据可追溯与质量可控。",
        gradient: "linear-gradient(45deg, #B9D5FF, #B9E7DA)",
        mockupImage: "./images/solution4.png" // GAP平台界面
    },
    {
        title: "药品电子说明书服务",
        description: "通过扫描药品包装上的二维码，语音播放听取药品说明书，避免纸质说明书字体小、阅读不便的问题，操作简单，一听就懂，让用药更安心、更便捷。",
        gradient: "linear-gradient(45deg, #D7F0FD, #D5DFFF)",
        mockupImage: "./images/service.png" // 四个手机截图的组合图片
    }
];

const otcData = [
    {
        title: "OTC管理平台",
        description: "聚焦OTC销售全流程闭环管理，支持药店档案管理、AI智能巡店、促销活动执行追踪、终端进销存、竞品动态等数据采集，自动完成销售行为考核与报表生成，通过目标下发-执行跟进-分析复盘数字化，助力销售提效、终端动销提升。",
        gradient: "linear-gradient(45deg, #FFF8D7, #FFA84E)",
        mockupImage: "./images/solution3-2fc3e5.png" // OTC管理平台界面
    },
    {
        title: "招商代理平台",
        description: "聚焦招商代理业务，覆盖代理商从筛选准入-签约洽谈-合作运营的全生命周期管理，通过终端档案、终端产品代理关系网、终端拜访协访、代理商拜访、代理业绩核算，串联线上线下内部外部业务，构建全流程可控、数据驱动的精益招商体系。",
        gradient: "linear-gradient(45deg, #FFFFFF, #5D95FE)",
        mockupImage: "./images/agent.png" // 招商代理平台界面
    },
    {
        title: "医药AI智能陪练系统",
        description: "基于青囊大模型，搭建医药AI智能陪练系统，让医药代表随时随地与AI扮演的不同医务人员对话，持续提升代表的沟通能力和表达技巧。",
        gradient: "linear-gradient(45deg, #A8F6FF, #5D95FE)",
        mockupImage: "./images/aiDoctor.png" // AI陪练系统界面
    }
];

// 轮播类
class Carousel {
    constructor(containerId, data, indicatorSelector, prevBtnId, nextBtnId) {
        this.container = document.getElementById(containerId);
        this.data = data;
        this.currentIndex = 0;
        this.indicators = document.querySelectorAll(`${indicatorSelector} .indicator`);
        this.prevBtn = document.getElementById(prevBtnId);
        this.nextBtn = document.getElementById(nextBtnId);
        this.autoPlayInterval = null;

        this.init();
    }

    init() {
        this.render();
        this.bindEvents();
        this.startAutoPlay();
    }

    render() {
        this.container.innerHTML = '';

        // 检查是否是解决方案轮播
        const isSolutionsCarousel = this.container.id === 'solutionsCarousel';

        if (isSolutionsCarousel) {
            // 解决方案轮播：创建2页，每页3个卡片
            const pages = [
                [this.data[0], this.data[1], this.data[2]], // 第一页：前3个
                [this.data[3], this.data[0], this.data[1]]  // 第二页：第4个 + 前2个
            ];

            pages.forEach((pageData, pageIndex) => {
                const carouselItem = document.createElement('div');
                carouselItem.className = 'carousel-item';

                let cardsHTML = '';
                pageData.forEach(item => {
                    cardsHTML += `
                        <div class="solution-card">
                            <div class="solution-card-image" style="background: ${item.gradient}">
                                <img src="${item.mockupImage}" alt="${item.title}">
                            </div>
                            <div class="solution-card-content">
                                <h3>${item.title}</h3>
                                <p>${item.description}</p>
                            </div>
                        </div>
                    `;
                });

                carouselItem.innerHTML = cardsHTML;
                this.container.appendChild(carouselItem);
            });
        } else {
            // OTC轮播保持原有布局
            this.data.forEach((item, index) => {
                const carouselItem = document.createElement('div');
                carouselItem.className = 'carousel-item';
                carouselItem.innerHTML = `
                    <div class="carousel-content">
                        <h3>${item.title}</h3>
                        <p>${item.description}</p>
                    </div>
                    <div class="carousel-image" style="background: ${item.gradient}">
                        <img src="${item.mockupImage}" alt="${item.title}">
                    </div>
                `;
                this.container.appendChild(carouselItem);
            });
        }

        this.updateCarousel();
    }

    updateCarousel() {
        const translateX = -this.currentIndex * 100;
        this.container.style.transform = `translateX(${translateX}%)`;

        // 更新指示器
        this.indicators.forEach((indicator, index) => {
            indicator.classList.toggle('active', index === this.currentIndex);
        });
    }

    getMaxIndex() {
        // 解决方案轮播只有2页，OTC轮播根据数据长度
        return this.container.id === 'solutionsCarousel' ? 1 : this.data.length - 1;
    }

    bindEvents() {
        // 指示器点击事件
        this.indicators.forEach((indicator, index) => {
            indicator.addEventListener('click', () => {
                this.currentIndex = index;
                this.updateCarousel();
                this.resetAutoPlay();
            });
        });

        // 前一个按钮
        if (this.prevBtn) {
            this.prevBtn.addEventListener('click', () => {
                this.prev();
                this.resetAutoPlay();
            });
        }

        // 下一个按钮
        if (this.nextBtn) {
            this.nextBtn.addEventListener('click', () => {
                this.next();
                this.resetAutoPlay();
            });
        }

        // 鼠标悬停暂停自动播放
        this.container.parentElement.addEventListener('mouseenter', () => {
            this.stopAutoPlay();
        });

        this.container.parentElement.addEventListener('mouseleave', () => {
            this.startAutoPlay();
        });
    }

    prev() {
        const maxIndex = this.getMaxIndex();
        this.currentIndex = this.currentIndex === 0 ? maxIndex : this.currentIndex - 1;
        this.updateCarousel();
    }

    next() {
        const maxIndex = this.getMaxIndex();
        this.currentIndex = this.currentIndex === maxIndex ? 0 : this.currentIndex + 1;
        this.updateCarousel();
    }

    startAutoPlay() {
        this.autoPlayInterval = setInterval(() => {
            this.next();
        }, 4000); // 4秒自动切换
    }

    stopAutoPlay() {
        if (this.autoPlayInterval) {
            clearInterval(this.autoPlayInterval);
            this.autoPlayInterval = null;
        }
    }

    resetAutoPlay() {
        this.stopAutoPlay();
        this.startAutoPlay();
    }
}

// 页面加载完成后初始化轮播
document.addEventListener('DOMContentLoaded', function() {
    // 初始化解决方案轮播
    const solutionsCarousel = new Carousel(
        'solutionsCarousel',
        solutionsData,
        '.solutions .carousel-indicators',
        'solutionsPrev',
        'solutionsNext'
    );

    // 初始化OTC轮播
    const otcCarousel = new Carousel(
        'otcCarousel',
        otcData,
        '.otc-section .carousel-indicators',
        'otcPrev',
        'otcNext'
    );

    // 平滑滚动导航
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // 导航栏滚动效果
    window.addEventListener('scroll', function() {
        const navbar = document.querySelector('.navbar');
        if (window.scrollY > 100) {
            navbar.style.background = 'rgba(255, 255, 255, 0.95)';
        } else {
            navbar.style.background = 'rgba(255, 255, 255, 0.5)';
        }
    });
});

// 添加触摸滑动支持
class TouchCarousel extends Carousel {
    constructor(containerId, data, indicatorSelector, prevBtnId, nextBtnId) {
        super(containerId, data, indicatorSelector, prevBtnId, nextBtnId);
        this.startX = 0;
        this.endX = 0;
        this.bindTouchEvents();
    }

    bindTouchEvents() {
        this.container.parentElement.addEventListener('touchstart', (e) => {
            this.startX = e.touches[0].clientX;
            this.stopAutoPlay();
        });

        this.container.parentElement.addEventListener('touchmove', (e) => {
            e.preventDefault();
        });

        this.container.parentElement.addEventListener('touchend', (e) => {
            this.endX = e.changedTouches[0].clientX;
            this.handleSwipe();
            this.startAutoPlay();
        });
    }

    handleSwipe() {
        const deltaX = this.startX - this.endX;
        const minSwipeDistance = 50;

        if (Math.abs(deltaX) > minSwipeDistance) {
            if (deltaX > 0) {
                this.next(); // 向左滑动，显示下一个
            } else {
                this.prev(); // 向右滑动，显示上一个
            }
        }
    }
}

// 在移动设备上使用触摸轮播
if ('ontouchstart' in window) {
    document.addEventListener('DOMContentLoaded', function() {
        // 重新初始化为触摸轮播
        const solutionsCarousel = new TouchCarousel(
            'solutionsCarousel',
            solutionsData,
            '.solutions .carousel-indicators',
            'solutionsPrev',
            'solutionsNext'
        );

        const otcCarousel = new TouchCarousel(
            'otcCarousel',
            otcData,
            '.otc-section .carousel-indicators',
            'otcPrev',
            'otcNext'
        );
    });
}

// 证书模态框功能
function openCertModal(imageSrc, altText) {
    const modal = document.getElementById('certModal');
    const modalImage = document.getElementById('modalImage');

    modalImage.src = imageSrc;
    modalImage.alt = altText;
    modal.style.display = 'block';

    // 防止背景滚动
    document.body.style.overflow = 'hidden';
}

function closeCertModal() {
    const modal = document.getElementById('certModal');
    modal.style.display = 'none';

    // 恢复背景滚动
    document.body.style.overflow = 'auto';
}

// 模态框事件监听
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('certModal');

    // 点击模态框背景关闭
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeCertModal();
        }
    });

    // ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeCertModal();
        }
    });
});